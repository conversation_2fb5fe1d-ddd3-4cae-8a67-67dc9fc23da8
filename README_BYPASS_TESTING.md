# escapeshellarg() Bypass Testing Suite

This collection of scripts tests various bypass techniques for PHP's `escapeshellarg()` function, with specific focus on the vulnerabilities found in osu-web's ImageProcessor.

## ⚠️ IMPORTANT WARNING

**These scripts are for educational and authorized security testing ONLY!**

- Only use on systems you own or have explicit permission to test
- Do not use against production systems without proper authorization
- Some tests may create files, execute commands, or modify system state
- Always run in safe mode first to understand what the scripts will do

## Scripts Overview

### 1. `escapeshellarg_bypass_tester.php`
**Main bypass testing script for PHP environments**

Tests comprehensive bypass techniques including:
- Platform-specific vulnerabilities (Windows/Unix)
- Character encoding issues
- Shell-specific bypasses
- Length-based attacks
- Context-specific injections
- Race conditions
- Parser confusion
- Modern bypass techniques

**Usage:**
```bash
# Safe mode (default) - shows what would be executed without running commands
php escapeshellarg_bypass_tester.php --safe-mode

# Live mode - actually executes commands (DANGEROUS!)
php escapeshellarg_bypass_tester.php
```

### 2. `osu_web_exploit_tester.php`
**Specific tests for osu-web ImageProcessor vulnerabilities**

Focuses on:
- Command injection via jhead
- File upload bypasses
- Malicious image creation (polyglot files)
- Symlink attacks
- Path traversal
- EXIF command injection

**Usage:**
```bash
# Safe mode (default)
php osu_web_exploit_tester.php

# Live mode - creates files and executes commands
php osu_web_exploit_tester.php --live
```

### 3. `advanced_bypass_tester.py`
**Advanced Python-based testing with additional techniques**

Includes:
- Encoding bypasses (URL, Base64, Hex)
- Race condition attacks
- Unicode normalization attacks
- Polyglot file creation
- Environment variable manipulation
- Platform-specific exploits

**Usage:**
```bash
# Safe mode (default)
python3 advanced_bypass_tester.py

# Live mode
python3 advanced_bypass_tester.py --live
```

## Prerequisites

### For PHP Scripts:
- PHP 7.4+ with CLI
- `jhead` command (for image processing tests)
- Write permissions to `/tmp` directory
- Unix-like environment recommended

### For Python Script:
- Python 3.6+
- PIL/Pillow library (optional, for image validation)
- Write permissions to temp directory

### Installation:
```bash
# Install jhead (Ubuntu/Debian)
sudo apt-get install jhead

# Install jhead (macOS)
brew install jhead

# Install Python dependencies
pip3 install Pillow
```

## Test Categories Explained

### 1. Platform-Specific Bypasses
Tests that exploit differences between Windows and Unix shells:
- Windows CMD ampersand (`&`) command chaining
- Unix semicolon (`;`) command separation
- Shell-specific metacharacters

### 2. Character Encoding Attacks
Exploits encoding issues that might bypass escaping:
- Multi-byte character exploits
- UTF-8 BOM injection
- Null byte injection (historical)
- Unicode normalization

### 3. Length-Based Attacks
Tests buffer overflow and truncation vulnerabilities:
- Very long arguments
- Buffer overflow attempts
- Argument length limits

### 4. Context-Specific Bypasses
Exploits based on how escaped arguments are used:
- SSH option injection (`-o ProxyCommand`)
- Command flag injection (`--help`)
- Filename interpretation issues

### 5. Race Condition Attacks
Tests timing-based vulnerabilities:
- File swapping during processing
- Symlink race conditions
- TOCTOU (Time of Check Time of Use) attacks

### 6. osu-web Specific Tests
Targets the actual vulnerabilities found:
- jhead command injection
- Image upload bypasses
- EXIF data exploitation
- File extension validation bypasses

## Understanding the Results

### Safe Mode Output
Shows what commands would be executed without actually running them:
```
Original payload: "test.txt'; echo BYPASSED"
Escaped payload: "'test.txt'\'''; echo BYPASSED'"
Full command: echo 'test.txt'\'''; echo BYPASSED'
SAFE MODE: Command not executed
```

### Live Mode Output
Actually executes commands and shows results:
```
Original payload: "test.txt'; echo BYPASSED"
Escaped payload: "'test.txt'\'''; echo BYPASSED'"
Full command: echo 'test.txt'\'''; echo BYPASSED'
Executing...
Return code: 0
Output: test.txt'; echo BYPASSED
```

### Success Indicators
- **BYPASSED**: Command injection was successful
- **Files created in /tmp**: Indicates successful code execution
- **Unexpected output**: May indicate partial bypass
- **Error messages**: Could indicate system protections

## Common Bypass Techniques Tested

### 1. Quote Escaping
```php
$payload = "file.txt'; echo BYPASSED; #";
// Attempts to break out of single quotes
```

### 2. Command Substitution
```php
$payload = "file.txt'$(echo BYPASSED)'";
// Uses command substitution
```

### 3. Environment Variables
```php
$payload = "file.txt\$IFS\$9echo\$IFS\$9BYPASSED";
// Uses Internal Field Separator
```

### 4. Unicode Confusion
```php
$payload = "file.txt\u2019; echo BYPASSED";
// Uses Unicode right single quote
```

### 5. Path Traversal
```php
$payload = "../../../etc/passwd";
// Attempts to access sensitive files
```

## Defensive Measures

If these tests reveal vulnerabilities, consider:

1. **Input Validation**: Validate all user input before processing
2. **Whitelist Approach**: Only allow known-safe characters/patterns
3. **Avoid Shell Execution**: Use native PHP functions instead of exec()
4. **Sandboxing**: Run commands in restricted environments
5. **File Type Validation**: Check file content, not just extensions
6. **Path Sanitization**: Resolve and validate all file paths

## Legal and Ethical Considerations

- **Authorization Required**: Only test systems you own or have permission to test
- **Responsible Disclosure**: Report vulnerabilities through proper channels
- **No Malicious Use**: These tools are for defensive security testing only
- **Documentation**: Keep records of authorized testing activities

## Troubleshooting

### Common Issues:
1. **Permission Denied**: Ensure write access to temp directories
2. **Command Not Found**: Install required tools (jhead, etc.)
3. **PHP Errors**: Check PHP version and CLI availability
4. **Python Errors**: Install required modules (Pillow)

### Safe Testing:
1. Always start with safe mode
2. Test in isolated environments
3. Monitor system resources during testing
4. Have cleanup procedures ready

## Contributing

When adding new bypass techniques:
1. Document the technique thoroughly
2. Include both safe and live mode support
3. Add appropriate warnings
4. Test on multiple platforms
5. Follow responsible disclosure practices

---

**Remember: With great power comes great responsibility. Use these tools ethically and legally.**
