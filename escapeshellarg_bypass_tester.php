<?php
/**
 * escapeshellarg() Bypass Testing Script
 * 
 * WARNING: This script is for educational and authorized security testing only.
 * Do not use this against systems you do not own or have explicit permission to test.
 * 
 * Usage: php escapeshellarg_bypass_tester.php [--safe-mode]
 */

class EscapeShellArgBypassTester
{
    private $safeMode = false;
    private $testResults = [];
    
    public function __construct($safeMode = false)
    {
        $this->safeMode = $safeMode;
        echo "=== escapeshellarg() Bypass Testing Script ===\n";
        echo "Safe Mode: " . ($safeMode ? "ON" : "OFF") . "\n";
        echo "Platform: " . PHP_OS . "\n";
        echo "PHP Version: " . PHP_VERSION . "\n\n";
        
        if (!$safeMode) {
            echo "WARNING: Running in LIVE mode - commands will be executed!\n";
            echo "Press Ctrl+C to abort or wait 5 seconds to continue...\n";
            sleep(5);
        }
    }
    
    public function runAllTests()
    {
        $this->testPlatformSpecific();
        $this->testCharacterEncoding();
        $this->testShellSpecific();
        $this->testLengthBased();
        $this->testContextSpecific();
        $this->testRaceConditions();
        $this->testParserConfusion();
        $this->testModernTechniques();
        
        $this->printResults();
    }
    
    private function testPayload($name, $payload, $command = 'echo', $description = '')
    {
        echo "\n--- Testing: $name ---\n";
        echo "Description: $description\n";
        echo "Original payload: " . var_export($payload, true) . "\n";
        
        $escaped = escapeshellarg($payload);
        echo "Escaped payload: " . var_export($escaped, true) . "\n";
        
        $fullCommand = "$command $escaped";
        echo "Full command: $fullCommand\n";
        
        if ($this->safeMode) {
            echo "SAFE MODE: Command not executed\n";
            $this->testResults[$name] = ['status' => 'safe_mode', 'command' => $fullCommand];
        } else {
            echo "Executing...\n";
            $output = [];
            $returnCode = 0;
            exec($fullCommand . " 2>&1", $output, $returnCode);
            
            echo "Return code: $returnCode\n";
            echo "Output: " . implode("\n", $output) . "\n";
            
            $this->testResults[$name] = [
                'status' => $returnCode === 0 ? 'executed' : 'failed',
                'command' => $fullCommand,
                'output' => $output,
                'return_code' => $returnCode
            ];
        }
        
        echo str_repeat("-", 50) . "\n";
    }
    
    private function testPlatformSpecific()
    {
        echo "\n=== PLATFORM-SPECIFIC TESTS ===\n";
        
        // Windows CMD.exe bypass attempts
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $this->testPayload(
                "Windows CMD Ampersand",
                "test.txt & echo BYPASSED",
                "type",
                "Windows CMD ampersand command chaining"
            );
            
            $this->testPayload(
                "Windows CMD Pipe",
                "test.txt | echo BYPASSED",
                "type",
                "Windows CMD pipe command"
            );
        }
        
        // Null byte injection (historical)
        $this->testPayload(
            "Null Byte Injection",
            "test.txt\0; echo BYPASSED",
            "cat",
            "Null byte termination (PHP < 5.3.0)"
        );
        
        // Newline injection
        $this->testPayload(
            "Newline Injection",
            "test.txt\n; echo BYPASSED",
            "cat",
            "Newline character injection"
        );
    }
    
    private function testCharacterEncoding()
    {
        echo "\n=== CHARACTER ENCODING TESTS ===\n";
        
        // Multi-byte character exploits
        $this->testPayload(
            "Multi-byte Exploit 1",
            "test.txt\x81\x27; echo BYPASSED",
            "cat",
            "Multi-byte character quote escape"
        );
        
        // UTF-8 BOM
        $this->testPayload(
            "UTF-8 BOM Injection",
            "\xEF\xBB\xBFtest.txt'; echo BYPASSED",
            "cat",
            "UTF-8 Byte Order Mark injection"
        );
        
        // High-bit characters
        $this->testPayload(
            "High-bit Characters",
            "test.txt\xFF'; echo BYPASSED",
            "cat",
            "High-bit character injection"
        );
    }
    
    private function testShellSpecific()
    {
        echo "\n=== SHELL-SPECIFIC TESTS ===\n";
        
        // Command substitution
        $this->testPayload(
            "Command Substitution",
            "test.txt'$(echo BYPASSED)'",
            "cat",
            "Bash command substitution"
        );
        
        // Backtick command substitution
        $this->testPayload(
            "Backtick Substitution",
            "test.txt'`echo BYPASSED`'",
            "cat",
            "Backtick command substitution"
        );
        
        // Environment variable expansion
        $this->testPayload(
            "Environment Variable",
            "test.txt\$HOME/../../bin/sh",
            "cat",
            "Environment variable expansion"
        );
        
        // IFS manipulation
        $this->testPayload(
            "IFS Manipulation",
            "test.txt\$IFS\$9echo\$IFS\$9BYPASSED",
            "cat",
            "Internal Field Separator manipulation"
        );
    }
    
    private function testLengthBased()
    {
        echo "\n=== LENGTH-BASED TESTS ===\n";
        
        // Very long argument
        $longPayload = str_repeat("A", 1000) . "'; echo BYPASSED; #";
        $this->testPayload(
            "Long Argument",
            $longPayload,
            "echo",
            "Very long argument to test buffer limits"
        );
        
        // Extremely long argument (potential buffer overflow)
        $veryLongPayload = str_repeat("B", 10000) . "'; echo BYPASSED; #";
        $this->testPayload(
            "Very Long Argument",
            $veryLongPayload,
            "echo",
            "Extremely long argument for buffer overflow"
        );
    }
    
    private function testContextSpecific()
    {
        echo "\n=== CONTEXT-SPECIFIC TESTS ===\n";

        // SSH-style option injection
        $this->testPayload(
            "SSH Option Injection",
            "-o ProxyCommand='echo BYPASSED'",
            "echo",
            "SSH option injection (if used with ssh command)"
        );

        // File flag injection
        $this->testPayload(
            "File Flag Injection",
            "--help'; echo BYPASSED; #",
            "cat",
            "Command flag injection"
        );

        // Dash filename
        $this->testPayload(
            "Dash Filename",
            "-",
            "cat",
            "Dash as filename (stdin)"
        );
    }

    private function testRaceConditions()
    {
        echo "\n=== RACE CONDITION TESTS ===\n";

        // Create test files for symlink attacks
        $testFile = "/tmp/test_" . uniqid() . ".txt";
        $symlinkFile = "/tmp/symlink_" . uniqid() . ".txt";

        if (!$this->safeMode) {
            file_put_contents($testFile, "original content");
            echo "Created test file: $testFile\n";
        }

        $this->testPayload(
            "Symlink Attack",
            $symlinkFile,
            "cat",
            "Symlink to sensitive file (manual symlink creation required)"
        );

        // Cleanup
        if (!$this->safeMode && file_exists($testFile)) {
            unlink($testFile);
        }
    }

    private function testParserConfusion()
    {
        echo "\n=== PARSER CONFUSION TESTS ===\n";

        // Complex quote nesting
        $this->testPayload(
            "Quote Nesting 1",
            "test.txt'\"'`echo BYPASSED`'\"'",
            "cat",
            "Complex quote nesting confusion"
        );

        // Mixed quote types
        $this->testPayload(
            "Quote Nesting 2",
            'test.txt"\'$(echo BYPASSED)\'"',
            "cat",
            "Mixed single and double quotes"
        );

        // Escaped quotes within quotes
        $this->testPayload(
            "Escaped Quotes",
            "test.txt\\''; echo BYPASSED; #",
            "cat",
            "Escaped single quote injection"
        );
    }

    private function testModernTechniques()
    {
        echo "\n=== MODERN BYPASS TECHNIQUES ===\n";

        // Path traversal
        $this->testPayload(
            "Path Traversal",
            "../../../etc/passwd",
            "cat",
            "Path traversal to sensitive files"
        );

        // Proc filesystem access
        $this->testPayload(
            "Proc Filesystem",
            "/proc/self/environ",
            "cat",
            "Access to process environment"
        );

        // Memory corruption attempt
        $memCorruptPayload = str_repeat("\x41", 1024) . "'; echo BYPASSED; #";
        $this->testPayload(
            "Memory Corruption",
            $memCorruptPayload,
            "echo",
            "Potential memory corruption"
        );

        // Unicode normalization
        $this->testPayload(
            "Unicode Normalization",
            "test.txt\u{2019}; echo BYPASSED",
            "cat",
            "Unicode right single quotation mark"
        );
    }

    private function testImageSpecificAttacks()
    {
        echo "\n=== IMAGE-SPECIFIC ATTACKS (osu-web context) ===\n";

        // Simulate jhead command injection
        $this->testPayload(
            "JHEAD Filename Injection",
            "'; echo 'JHEAD BYPASSED' > /tmp/jhead_test; #.jpg",
            "jhead -autorot -purejpg -q",
            "jhead command injection via filename"
        );

        // Create malicious image file for testing
        if (!$this->safeMode) {
            $maliciousImage = "/tmp/malicious_" . uniqid() . ".jpg";

            // Create a minimal JPEG with malicious EXIF
            $jpegHeader = "\xFF\xD8\xFF\xE1"; // JPEG + EXIF marker
            $exifData = "'; echo 'EXIF BYPASSED' > /tmp/exif_test; #";
            $jpegEnd = "\xFF\xD9";

            file_put_contents($maliciousImage, $jpegHeader . pack("n", strlen($exifData) + 2) . $exifData . $jpegEnd);

            $this->testPayload(
                "Malicious EXIF Data",
                $maliciousImage,
                "jhead -autorot -purejpg -q",
                "EXIF data containing shell commands"
            );

            // Cleanup
            if (file_exists($maliciousImage)) {
                unlink($maliciousImage);
            }
        }
    }

    private function printResults()
    {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "SUMMARY OF TEST RESULTS\n";
        echo str_repeat("=", 60) . "\n";

        $successful = 0;
        $failed = 0;
        $safeMode = 0;

        foreach ($this->testResults as $testName => $result) {
            echo sprintf("%-30s: %s\n", $testName, strtoupper($result['status']));

            switch ($result['status']) {
                case 'executed':
                    $successful++;
                    break;
                case 'failed':
                    $failed++;
                    break;
                case 'safe_mode':
                    $safeMode++;
                    break;
            }
        }

        echo "\nStatistics:\n";
        echo "- Successful executions: $successful\n";
        echo "- Failed executions: $failed\n";
        echo "- Safe mode tests: $safeMode\n";
        echo "- Total tests: " . count($this->testResults) . "\n";

        if ($successful > 0 && !$this->safeMode) {
            echo "\nWARNING: Some bypass attempts were successful!\n";
            echo "This indicates potential vulnerabilities in your system.\n";
        }
    }
}

// Command line interface
$safeMode = in_array('--safe-mode', $argv);

$tester = new EscapeShellArgBypassTester($safeMode);
$tester->runAllTests();

echo "\nTesting completed.\n";
echo "Remember: This script is for educational and authorized testing only!\n";
?>
